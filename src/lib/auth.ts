import { betterAuth } from "better-auth";
import { genericOAuth } from "better-auth/plugins";


export const auth = betterAuth({
  plugins: [
    genericOAuth({
      config: {
        providerId: "wecom",
        clientId: "ww06d937737e06663a",
        clientSecret: "e11111111111111111111111111111111",
        authorizationUrl: "https://open.weixin.qq.com/connect/oauth2/authorize",
        tokenUrl: "https://qyapi.weixin.qq.com/cgi-bin/gettoken",
        userInfoUrl: "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo",      }
    })
  ]
})